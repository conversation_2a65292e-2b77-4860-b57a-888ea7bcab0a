{"private": true, "name": "IBLIS", "scripts": {"build": "nuxt build", "app": "concurrently \"npm run docs:preview\" \"npm run dev\"", "dev": "nuxt dev", "generate": "nuxt generate", "git-tag": "tsc version.ts && node version.js && rm version.js", "preview": "nuxt preview", "postinstall": "nuxt prepare", "docs:dev": "vitepress dev docs", "docs:build": "vitepress build docs", "docs:preview": "vitepress preview docs", "mi:dev": "vitepress dev machine-integration", "mi:build": "vitepress build machine-integration", "mi:preview": "vitepress preview machine-integration"}, "devDependencies": {"@pinia-plugin-persistedstate/nuxt": "^1.2.1", "@types/uuid": "^10.0.0", "concurrently": "^8.2.2", "nitro-public-module": "^0.1.2", "nuxt": "^3.12.2", "vitepress": "^1.0.0-rc.25"}, "dependencies": {"@formkit/icons": "^0.16.4", "@formkit/nuxt": "^0.16.2", "@formkit/vue": "^0.16.2", "@headlessui/vue": "^1.7.22", "@heroicons/vue": "^2.0.14", "@nuxt/types": "^2.16.3", "@nuxt/vite-builder": "^3.2.3", "@nuxtjs/tailwindcss": "^6.3.1", "@pinia/nuxt": "^0.5.1", "@tailwindcss/line-clamp": "^0.4.2", "@types/file-saver": "^2.0.5", "@types/fs-extra": "^11.0.2", "@types/lodash": "^4.14.195", "@types/node": "^20.6.1", "@types/vue": "^2.0.0", "@unhead/vue": "^1.1.23", "@vueform/multiselect": "^2.6.11", "@vuepic/vue-datepicker": "^5.1.2", "@vueup/vue-quill": "^1.2.0", "chart.js": "^4.2.1", "child_process": "^1.0.2", "file-saver": "^2.0.5", "fs-extra": "^11.1.1", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.1", "jsbarcode": "^3.11.5", "jspdf": "^2.5.1", "lodash": "^4.17.21", "mermaid": "^10.9.0", "moment": "^2.29.4", "nitro": "^0.0.0", "node-sass": "^8.0.0", "pinia": "^2.0.33", "pinia-plugin-persistedstate": "^3.2.1", "quagga": "^0.6.16", "sass": "^1.58.0", "tailwindcss": "^3.2.4", "uuid": "^11.1.0", "vitepress-plugin-mermaid": "^2.0.16", "vue": "3", "vue-chartjs": "^5.2.0", "vue-doc-exporter": "^1.3.3", "vue-json-excel3": "^0.0.9", "vue-router": "^4.1.6", "vue3-barcode": "^1.0.1", "vue3-easy-data-table": "^1.5.31", "vue3-toastify": "^0.1.5", "vuex": "^4.1.0"}, "version": "v3.2.9-beta"}